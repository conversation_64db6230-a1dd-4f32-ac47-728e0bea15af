import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Mail, RefreshCw, Trash, Send } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import { useTeamData } from './hooks/useTeamData';

interface SentInvitationsDisplayProps {
  teamId: string;
}

export default function SentInvitationsDisplay({ teamId }: SentInvitationsDisplayProps) {
  const {
    sentInvitations,
    loadingSentInvitations,
    fetchSentInvitations,
    deleteInvitation,
    resendInvitation
  } = useTeamData(teamId);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (isRefreshing || !teamId) return;

    setIsRefreshing(true);
    try {
      await fetchSentInvitations(teamId);
      toast.success('Invitations refreshed');
    } catch (error) {
      console.error('Error refreshing invitations:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleDelete = async (invitationId: string) => {
    if (!teamId) return;

    const confirmed = window.confirm('Are you sure you want to delete this invitation?');
    if (!confirmed) return;

    await deleteInvitation(invitationId);
  };

  const handleResend = async (invitationId: string) => {
    if (!teamId) return;
    await resendInvitation(invitationId);
  };

  if (loadingSentInvitations) {
    return (
      <Card className="p-4 sm:p-6">
        <div className="flex items-center justify-between gap-2 mb-4">
          <div className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-muted-foreground" />
            <h2 className="font-semibold">Sent Invitations</h2>
          </div>
          <Button variant="outline" size="sm" disabled>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
        <div className="space-y-4">
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
        </div>
      </Card>
    );
  }

  if (sentInvitations.length === 0) {
    return (
      <Card className="p-4 sm:p-6">
        <div className="flex items-center justify-between gap-2 mb-4">
          <div className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-muted-foreground" />
            <h2 className="font-semibold">Sent Invitations</h2>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
        <p className="text-muted-foreground text-center py-4">No pending invitations sent</p>
      </Card>
    );
  }

  return (
    <Card className="p-4 sm:p-6">
      <div className="flex items-center justify-between gap-2 mb-4">
        <div className="flex items-center gap-2">
          <Mail className="h-5 w-5 text-blue-600" />
          <h2 className="font-semibold">Sent Invitations</h2>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>
      <div className="space-y-4">
        {sentInvitations.map((invitation) => (
          <div
            key={invitation.id}
            className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 p-3 bg-white rounded-md border"
          >
            <div>
              <p className="font-medium">{invitation.email}</p>
              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 text-sm text-muted-foreground">
                <span>Role: {invitation.role === 'service_provider' ? 'Service Provider' : invitation.role}</span>
                <span className="hidden sm:inline">•</span>
                <span>Sent: {formatDistanceToNow(new Date(invitation.created_at), { addSuffix: true })}</span>
                <span className="hidden sm:inline">•</span>
                <span>Expires: {formatDistanceToNow(new Date(invitation.expires_at), { addSuffix: true })}</span>
              </div>
            </div>
            <div className="flex gap-2 w-full sm:w-auto mt-2 sm:mt-0">
              <Button
                variant="outline"
                size="sm"
                className="flex-1 sm:flex-none"
                onClick={() => handleResend(invitation.id)}
              >
                <Send className="h-4 w-4 mr-2" />
                Resend
              </Button>
              <Button
                variant="destructive"
                size="sm"
                className="flex-1 sm:flex-none"
                onClick={() => handleDelete(invitation.id)}
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}