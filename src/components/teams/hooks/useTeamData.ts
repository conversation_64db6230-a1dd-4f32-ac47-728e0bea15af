
import { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useTeamManagement } from '@/hooks/useTeamManagement';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissions';
import { PermissionType } from '@/types/auth';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

export const useTeamData = (teamId: string) => {
  const { authState } = useAuth();
  const queryClient = useQueryClient();

  // Get team members using React Query with team-specific key
  const {
    data: rawTeamMembers = [],
    isLoading: loadingTeamMembers,
    error: teamMembersError
  } = useQuery({
    queryKey: ['teamMembers', teamId],
    queryFn: async () => {
      if (!teamId) return [];

      console.log(`[useTeamData] Fetching team members for team ${teamId}`);

      const { data, error } = await supabase.rpc('get_team_members', { p_team_id: teamId });

      if (error) {
        console.error('[useTeamData] Error fetching team members:', error);
        throw error;
      }

      console.log(`[useTeamData] Found ${data?.length || 0} team members for team ${teamId}`);

      // Transform the data to match the expected TeamMember format
      return (data || []).map((member: any) => {
        // Split user_name back into first_name and last_name
        const nameParts = (member.user_name || '').split(' ');
        const first_name = nameParts[0] || '';
        const last_name = nameParts.slice(1).join(' ') || '';

        return {
          id: member.id,
          user_id: member.user_id,
          team_id: member.team_id,
          email: member.user_email,
          first_name,
          last_name,
          status: member.status,
          avatar_url: null, // RPC doesn't return this
          created_at: member.created_at,
          profile_role: member.role
        };
      });
    },
    enabled: !!teamId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  // Use the transformed data
  const teamMembers = rawTeamMembers;

  // Get sent invitations using React Query with team-specific key
  const {
    data: sentInvitations = [],
    isLoading: loadingInvitations,
    error: invitationsError
  } = useQuery({
    queryKey: ['teamInvitations', teamId],
    queryFn: async () => {
      if (!teamId) return [];

      console.log(`[useTeamData] Fetching sent invitations for team ${teamId}`);

      const { data, error } = await supabase
        .from('team_invitations')
        .select('*')
        .eq('team_id', teamId)
        .eq('status', 'pending');

      if (error) {
        console.error('[useTeamData] Error fetching sent invitations:', error);
        throw error;
      }

      console.log(`[useTeamData] Found ${data?.length || 0} sent invitations for team ${teamId}`);
      return data || [];
    },
    enabled: !!teamId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });

  // Get teams and other functions from the old hook
  const {
    teams,
    loading: generalLoading,
    inviteUserToTeam,
    removeTeamMember,
    deleteInvitation,
    resendInvitation
  } = useTeamManagement();

  const { hasPermission } = usePermissions();

  const [isProcessing, setIsProcessing] = useState(false);

  const isTeamOwner = teams.find(t => t.id === teamId)?.owner_id === authState.user?.id;
  const isAdmin = authState?.profile?.role === 'admin' || authState?.profile?.is_super_admin;
  const isPropertyManager = authState?.profile?.role === 'property_manager';

  const canManageStaff = isTeamOwner || isAdmin || isPropertyManager || hasPermission(PermissionType.MANAGE_STAFF, teamId);
  const canManageProviders = isTeamOwner || isAdmin || isPropertyManager || hasPermission(PermissionType.MANAGE_SERVICE_PROVIDERS, teamId);
  const canInviteMembers = isTeamOwner || isAdmin || isPropertyManager || canManageStaff || canManageProviders;

  // Helper functions for manual refresh
  const fetchTeamMembers = async (teamId: string) => {
    await queryClient.invalidateQueries({ queryKey: ['teamMembers', teamId] });
  };

  const fetchSentInvitations = async (teamId: string) => {
    await queryClient.invalidateQueries({ queryKey: ['teamInvitations', teamId] });
  };

  return {
    // Team Data
    teamMembers,
    teams,
    sentInvitations,

    // Loading States
    loading: generalLoading || loadingTeamMembers,
    loadingSentInvitations: loadingInvitations,
    isProcessing,

    // User Permissions
    isTeamOwner,
    isAdmin,
    isPropertyManager,
    canManageStaff,
    canManageProviders,
    canInviteMembers,

    // Functions
    fetchTeamMembers,
    inviteUserToTeam,
    removeTeamMember,
    fetchSentInvitations,
    deleteInvitation,
    resendInvitation,
    setIsProcessing,
    authState,
  };
};
