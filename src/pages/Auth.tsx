import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/auth';
import { supabase } from '@/integrations/supabase/client';
import { acceptInvitationDirect, processInvitationAcceptance, registerUser, registerUserForInvitation, signInUser } from '@/api/invitations';

const Auth = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { signIn, signUp } = useAuth();
  const [activeTab, setActiveTab] = useState<'login' | 'register'>('login');
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [registerEmail, setRegisterEmail] = useState('');
  const [registerPassword, setRegisterPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [role, setRole] = useState<UserRole>('property_manager');
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);

  useEffect(() => {
    // Parse URL parameters
    const urlParams = new URLSearchParams(location.search);
    const returnTo = urlParams.get('return_to');
    const inviteRole = urlParams.get('role');
    const invitationToken = urlParams.get('invitation_token') || urlParams.get('invitation');

    // Check if there's an invitation token in the URL
    if (invitationToken) {
      console.log('Found invitation token in URL:', invitationToken);
      localStorage.setItem('pendingInvitation', invitationToken);

      // Set the active tab to register by default for invitations
      setActiveTab('register');

      // Fetch invitation details to pre-fill the form
      const fetchInvitationDetails = async () => {
        try {
          const { data, error } = await supabase
            .from('team_invitations')
            .select('*')
            .eq('token', invitationToken)
            .single();

          if (error) {
            console.error('Error fetching invitation details:', error);
            return;
          }

          if (data) {
            console.log('Found invitation details:', data);

            // Pre-fill the email field
            if (data.email) {
              setRegisterEmail(data.email);
              localStorage.setItem('pendingInvitationEmail', data.email);
            }

            // Set the role and auto-select it
            if (data.role) {
              setRole(data.role as UserRole);
              localStorage.setItem('pendingInvitationRole', data.role);
              
              // Auto-select the role dropdown if it exists
              setTimeout(() => {
                const roleSelect = document.querySelector('[data-testid="role-select"], select[name*="role"]');
                if (roleSelect) {
                  (roleSelect as HTMLSelectElement).value = data.role;
                }
              }, 100);
            }

            // Store team information
            if (data.team_id) {
              localStorage.setItem('pendingInvitationTeamId', data.team_id);
            }

            if (data.team_name) {
              localStorage.setItem('pendingInvitationTeamName', data.team_name);
            }
          }
        } catch (error) {
          console.error('Exception fetching invitation details:', error);
        }
      };

      fetchInvitationDetails();
    }

    // Check if there's an email in the invitation to pre-fill
    const pendingEmail = urlParams.get('email');
    if (pendingEmail) {
      localStorage.setItem('pendingInvitationEmail', pendingEmail);
      setRegisterEmail(pendingEmail);
      setLoginEmail(pendingEmail);
      // Switch to register tab by default if we have an email
      setActiveTab('register');
      toast.info("Please create an account to accept the team invitation");
    } else {
      // If no email is provided, check if we're coming from an invitation page
      if (returnTo?.includes('/invite')) {
        // Try to extract email from the invitation
        const invitationEmail = localStorage.getItem('pendingInvitationEmail');
        if (invitationEmail) {
          setRegisterEmail(invitationEmail);
          setLoginEmail(invitationEmail);
        }
      }
    }

    // Set role based on URL parameter or localStorage
    if (inviteRole === 'service_provider' || inviteRole === 'staff') {
      setRole(inviteRole);
    } else {
      // Check if we have a role stored from an invitation
      const storedRole = localStorage.getItem('pendingInvitationRole');
      if (storedRole === 'service_provider' || storedRole === 'staff') {
        setRole(storedRole as UserRole);
      }
    }
  }, [location.search]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoggingIn(true);

    try {
      console.log('Attempting to sign in with email:', loginEmail);
      const { error } = await signIn(loginEmail, loginPassword);
      if (error) throw error;

      console.log('Sign in successful, checking for pending invitation');

      // Check for team invitation
      const pendingInvitation = localStorage.getItem('pendingInvitation');
      const pendingTeamId = localStorage.getItem('pendingInvitationTeamId');

      if (pendingInvitation) {
        console.log('Found pending invitation, redirecting to invitation page');

        // Construct the invitation URL with team_id if available
        let inviteUrl = `/invite?token=${pendingInvitation}`;
        if (pendingTeamId) {
          inviteUrl += `&team_id=${pendingTeamId}`;
        }

        // Don't clear localStorage yet - we'll do that after the invitation is accepted
        // This allows the invitation page to access the invitation details

        // Navigate to the invitation page
        navigate(inviteUrl);
        return;
      }

      // Check for return URL
      const urlParams = new URLSearchParams(location.search);
      const returnTo = urlParams.get('return_to');
      if (returnTo) {
        // Make sure the return URL starts with a slash
        const formattedReturnTo = returnTo.startsWith('/') ? returnTo : `/${returnTo}`;
        navigate(formattedReturnTo);
      } else {
        // Default redirect to dashboard
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error((error as any).message || 'Login failed. Please check your credentials and try again.');
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsRegistering(true);

    try {
      // Determine the role to use for registration
      let effectiveRole = role;

      // Check if we're coming from an invitation
      const pendingInvitation = localStorage.getItem('pendingInvitation');
      const pendingEmail = localStorage.getItem('pendingInvitationEmail');

      if (pendingInvitation) {
        console.log('Registering for a pending invitation');

        // If we have a pending invitation, check if we have a role stored
        const inviteRole = localStorage.getItem('pendingInvitationRole');

        // Validate the role is one of the allowed values
        const validRoles = ['admin', 'property_manager', 'service_provider', 'staff', 'super_admin'];

        if (inviteRole) {
          // Use the role from the invitation if it's valid
          if (validRoles.includes(inviteRole)) {
            console.log('Using role from invitation:', inviteRole);
            effectiveRole = inviteRole as UserRole;
            // Store it for later use
            localStorage.setItem('pendingInvitationRole', inviteRole);
          } else {
            console.log('Invalid role found for invitation, defaulting to service_provider');
            effectiveRole = 'service_provider';
          }
        } else {
          // Use the selected role if it's valid
          if (validRoles.includes(role)) {
            console.log('Using selected role from form:', role);
            effectiveRole = role as UserRole;
          } else {
            console.log('Invalid role selected, defaulting to service_provider');
            effectiveRole = 'service_provider';
          }
        }

        // Log the invitation details for debugging
        const pendingTeamId = localStorage.getItem('pendingInvitationTeamId');
        const pendingTeamName = localStorage.getItem('pendingInvitationTeamName');
        console.log('Invitation details:', {
          token: pendingInvitation,
          email: pendingEmail,
          role: effectiveRole,
          teamId: pendingTeamId,
          teamName: pendingTeamName
        });

        // For invitation signups, we'll just register normally and handle the invitation later
        console.log('Handling invitation registration normally');

        // Store the invitation details for later
        localStorage.setItem('pendingRegistrationEmail', registerEmail);
        localStorage.setItem('pendingRegistrationPassword', registerPassword);
        localStorage.setItem('pendingRegistrationFirstName', firstName);
        localStorage.setItem('pendingRegistrationLastName', lastName);
        localStorage.setItem('pendingRegistrationRole', effectiveRole);

        // Continue with normal registration
        console.log('Continuing with normal registration flow');
      }

      // Regular signup flow (for both invitation and non-invitation signups)
      console.log('Registering with role:', effectiveRole, 'for email:', registerEmail);

      // For invitation signups, we'll use a different approach
      if (pendingInvitation) {
        console.log('This is an invitation signup, using direct invitation acceptance');

        try {
          // Use the new approach that auto-confirms email for invitation users
          console.log('Using auto-confirmed registration for invitation users');

          const registerResult = await registerUserForInvitation(
            pendingInvitation,
            registerEmail,
            registerPassword,
            firstName,
            lastName,
            effectiveRole
          );

          if (registerResult.success) {
            console.log('Invitation processed successfully!');
            
            // Show appropriate message based on user status
            if (registerResult.already_member) {
              toast.info(registerResult.message || 'You are already a member of this team!');
            } else if (registerResult.existing_user) {
              toast.success(registerResult.message || 'Welcome back! You have been added to the team.');
            } else {
              toast.success(registerResult.message || 'Welcome! Your account has been created and you have been added to the team.');
            }

            // Clear invitation data from localStorage
            localStorage.removeItem('pendingInvitation');
            localStorage.removeItem('pendingInvitationEmail');
            localStorage.removeItem('pendingInvitationRole');
            localStorage.removeItem('pendingInvitationTeamId');
            localStorage.removeItem('pendingInvitationTeamName');

            // Redirect to teams page
            navigate('/teams');
            return;
          } else {
            console.error('Failed to register user for invitation:', registerResult.error);
            
            // If error mentions user already exists, try to sign in
            if (registerResult.error && (
              registerResult.error.includes('already exists') ||
              registerResult.error.includes('duplicate') ||
              registerResult.error.includes('already registered')
            )) {
              console.log('User already exists, attempting sign in...');
              toast.info('Account already exists, trying to sign you in...');
              
              const { error: signInError } = await signIn(registerEmail, registerPassword);
              if (!signInError) {
                // Try to process the invitation for the existing user
                const { data: { user } } = await supabase.auth.getUser();
                if (user) {
                  const invitationResult = await processInvitationAcceptance(pendingInvitation, user.id);
                  if (invitationResult.success) {
                    toast.success('Welcome back! Invitation accepted successfully!');
                    navigate('/teams');
                    return;
                  }
                }
                navigate('/dashboard');
                return;
              } else {
                toast.error('Account exists but password is incorrect. Please use the correct password.');
                setLoginEmail(registerEmail);
                setActiveTab('login');
                return;
              }
            } else {
              toast.error(`Registration failed: ${registerResult.error}`);
              return;
            }
          }
        } catch (invitationError) {
          console.error('Failed to accept invitation directly:', invitationError);
          // Don't show an error toast here, as we're falling back to the regular flow
          // toast.error(`Failed to accept invitation: ${(invitationError as any).message || 'Unknown error'}`);

          // Fall back to regular signup flow
          console.log('Falling back to regular signup flow');
        }
      }

      // Regular signup flow (for non-invitation signups or if invitation acceptance failed)
      try {
        // First, try to sign in with the provided credentials
        // This helps avoid the 500 error when trying to create a user that already exists
        try {
          console.log('Checking if user already exists by attempting sign in first');
          const { error: signInError } = await signIn(registerEmail, registerPassword);

          if (!signInError) {
            console.log('User already exists and credentials are valid');

            // If this is for an invitation, redirect to the invitation page
            if (pendingInvitation) {
              console.log('User exists and has a pending invitation, redirecting to invitation page');
              const pendingTeamId = localStorage.getItem('pendingInvitationTeamId');
              let inviteUrl = `/invite?token=${pendingInvitation}`;
              if (pendingTeamId) {
                inviteUrl += `&team_id=${pendingTeamId}`;
              }
              navigate(inviteUrl);
              return;
            } else {
              // For regular sign-in, redirect to dashboard
              console.log('User exists, redirecting to dashboard');
              navigate('/dashboard');
              return;
            }
          } else {
            console.log('User does not exist or credentials are invalid, will create new user');
          }
        } catch (signInError) {
          console.error('Error checking if user exists:', signInError);
          // Continue with signup flow
        }

        // If we get here, the user doesn't exist or the credentials are invalid
        // Proceed with signup
        console.log('Proceeding with signup for email:', registerEmail, 'with role:', effectiveRole);

        // New approach: Separate registration and invitation acceptance
        if (pendingInvitation) {
          console.log('Using new approach for invitation signup');

          // Step 1: Try to sign in first (in case user already exists)
          const signInResult = await signInUser(registerEmail, registerPassword);

          if (signInResult.success) {
            console.log('User already exists and signed in successfully:', signInResult.user.id);

            // Step 2: Process the invitation
            const invitationResult = await processInvitationAcceptance(pendingInvitation, signInResult.user.id);

            if (invitationResult.success) {
              console.log('Invitation processed successfully:', invitationResult);
              toast.success('Invitation accepted successfully! Redirecting...');

              // Always redirect to the teams page since we don't have a specific team page route
              navigate('/teams');
              return;
            } else {
              console.error('Failed to process invitation:', invitationResult.error);
              toast.error(`Failed to accept invitation: ${invitationResult.error}`);
              return;
            }
          } else {
            // User doesn't exist or password is incorrect
            console.log('User does not exist or password is incorrect, trying to register');

            // Step 1: Register the user
            const registerResult = await registerUser(registerEmail, registerPassword, firstName, lastName, effectiveRole);

            if (registerResult.success) {
              console.log('User registered successfully:', registerResult.user.id);
              toast.success('Account created successfully!');

              // Step 2: Process the invitation
              const invitationResult = await processInvitationAcceptance(pendingInvitation, registerResult.user.id);

              if (invitationResult.success) {
                console.log('Invitation processed successfully:', invitationResult);
                toast.success('Invitation accepted successfully! Redirecting...');

                // Always redirect to the teams page since we don't have a specific team page route
                navigate('/teams');
                return;
              } else {
                console.error('Failed to process invitation:', invitationResult.error);
                toast.error(`Failed to accept invitation: ${invitationResult.error}`);

                // Still try to sign in the user
                const { error: signInError } = await signIn(registerEmail, registerPassword);
                if (!signInError) {
                  navigate('/dashboard');
                } else {
                  setLoginEmail(registerEmail);
                  setActiveTab('login');
                }
                return;
              }
            } else if (registerResult.error && (
              registerResult.error.includes('already exists') ||
              registerResult.error.includes('duplicate key') ||
              registerResult.error.includes('already registered')
            )) {
              // User already exists but password was incorrect
              console.log('User already exists but password was incorrect');
              toast.error('This email is already registered. Please use the correct password or reset it.');

              // Switch to login tab with email pre-filled
              setLoginEmail(registerEmail);
              setActiveTab('login');
              return;
            } else {
              // Other registration error
              console.error('Registration failed:', registerResult.error);
              toast.error(`Registration failed: ${registerResult.error}`);
              return;
            }
          }
        }

        // For regular signups (non-invitation), use the normal flow
        const { error, emailConfirmation } = await signUp(registerEmail, registerPassword, firstName, lastName, effectiveRole);
        if (error) {
          console.error('Signup error:', error);
          // If we get a database error, it might be because the user already exists
          if (error.message && (error.message.includes('Database error saving new user') || error.message.includes('User already registered'))) {
            console.log('User might already exist despite sign-in check, trying to sign in again');

            // Try to sign in with the provided credentials
            const { error: signInError } = await signIn(registerEmail, registerPassword);
            if (!signInError) {
              console.log('Successfully signed in existing user');

              // If this is for an invitation, redirect to the invitation page
              if (pendingInvitation) {
                console.log('User exists and has a pending invitation, redirecting to invitation page');
                const pendingTeamId = localStorage.getItem('pendingInvitationTeamId');
                let inviteUrl = `/invite?token=${pendingInvitation}`;
                if (pendingTeamId) {
                  inviteUrl += `&team_id=${pendingTeamId}`;
                }
                navigate(inviteUrl);
                return;
              } else {
                // For regular sign-in, redirect to dashboard
                console.log('User exists, redirecting to dashboard');
                navigate('/dashboard');
                return;
              }
            } else {
              console.error('Failed to sign in existing user:', signInError);
              throw error; // Keep the original error if sign-in fails
            }
          } else {
            throw error;
          }
        }

        console.log('Registration successful, emailConfirmation:', emailConfirmation);
        toast.success('Registration successful! Logging you in...');

        // Auto login after registration
        try {
          // Wait a moment for the registration to complete
          await new Promise(resolve => setTimeout(resolve, 1000));

          const { error: loginError } = await signIn(registerEmail, registerPassword);
          if (loginError) {
            console.error('Auto-login failed:', loginError);
            toast.error('Auto-login failed. Please try logging in manually.');

            // Switch to login tab with email pre-filled
            setLoginEmail(registerEmail);
            setActiveTab('login');
          } else {
            console.log('Auto-login successful');

            // If this is for an invitation, redirect to the invitation page
            if (pendingInvitation) {
              console.log('Auto-login successful with pending invitation, redirecting to invitation page');
              const pendingTeamId = localStorage.getItem('pendingInvitationTeamId');
              let inviteUrl = `/invite?token=${pendingInvitation}`;
              if (pendingTeamId) {
                inviteUrl += `&team_id=${pendingTeamId}`;
              }
              navigate(inviteUrl);
            } else {
              // For regular sign-in, redirect to dashboard
              navigate('/dashboard');
            }
          }
        } catch (loginError) {
          console.error('Auto-login exception:', loginError);
          toast.error('Auto-login failed. Please try logging in manually.');

          // Switch to login tab with email pre-filled
          setLoginEmail(registerEmail);
          setActiveTab('login');
        }
      } catch (signupError) {
        console.error('Signup error:', signupError);
        throw signupError;
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast.error((error as any).message || 'Registration failed. Please try again.');
    } finally {
      setIsRegistering(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">StayFu</CardTitle>
          <CardDescription className="text-center">
            {role === 'service_provider'
              ? 'Join as a Service Provider'
              : 'Property management made simple'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'login' | 'register')} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="login">Login</TabsTrigger>
              <TabsTrigger value="register">Register</TabsTrigger>
            </TabsList>

            <TabsContent value="login">
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium">Email</label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={loginEmail}
                    onChange={(e) => setLoginEmail(e.target.value)}
                    required
                    autoComplete="email"
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <label htmlFor="password" className="text-sm font-medium">Password</label>
                    <Button
                      variant="link"
                      className="p-0 h-auto text-xs"
                      type="button"
                      onClick={() => navigate('/#/forgot-password')}
                    >
                      Forgot password?
                    </Button>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={loginPassword}
                    onChange={(e) => setLoginPassword(e.target.value)}
                    required
                    autoComplete="current-password"
                  />
                </div>
                <Button type="submit" className="w-full" disabled={isLoggingIn}>
                  {isLoggingIn ? 'Logging in...' : 'Login'}
                </Button>
              </form>
            </TabsContent>

            <TabsContent value="register">
              <form onSubmit={handleRegister} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="firstName" className="text-sm font-medium">First Name</label>
                    <Input
                      id="firstName"
                      placeholder="John"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      required
                      autoComplete="given-name"
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="lastName" className="text-sm font-medium">Last Name</label>
                    <Input
                      id="lastName"
                      placeholder="Doe"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      required
                      autoComplete="family-name"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="registerEmail" className="text-sm font-medium">Email</label>
                  <Input
                    id="registerEmail"
                    type="email"
                    placeholder="<EMAIL>"
                    value={registerEmail}
                    onChange={(e) => setRegisterEmail(e.target.value)}
                    required
                    autoComplete="email"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="registerPassword" className="text-sm font-medium">Password</label>
                  <Input
                    id="registerPassword"
                    type="password"
                    placeholder="••••••••"
                    value={registerPassword}
                    onChange={(e) => setRegisterPassword(e.target.value)}
                    required
                    minLength={8}
                    autoComplete="new-password"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="role" className="text-sm font-medium">Account Type</label>
                  <Select
                    value={role}
                    onValueChange={(value: string) => setRole(value as UserRole)}
                    data-testid="role-select"
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select your account type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="property_manager">Property Manager</SelectItem>
                      <SelectItem value="service_provider">Service Provider</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button type="submit" className="w-full" disabled={isRegistering}>
                  {isRegistering ? 'Creating account...' : 'Create account'}
                </Button>
                {role === 'service_provider' && (
                  <p className="text-xs text-muted-foreground mt-2">
                    By registering as a service provider, you'll be able to accept and manage maintenance tasks.
                  </p>
                )}
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default Auth;
