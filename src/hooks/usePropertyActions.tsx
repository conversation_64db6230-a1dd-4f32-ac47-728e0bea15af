
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Property } from './useProperties';
import { fetchCalendarData } from '@/utils/propertyUtils';
import { Property as CardProperty, CollectionWithBudget } from '@/components/properties/PropertyCard';
import { usePermissions } from '@/hooks/usePermissions';
import { PermissionType } from '@/types/auth';

export const usePropertyActions = (
  userId: string | undefined,
  properties: Property[],
  setProperties: React.Dispatch<React.SetStateAction<Property[]>>,
  fetchProperties: () => Promise<void>
) => {
  const { hasPermission } = usePermissions();
  const [isAddPropertyOpen, setIsAddPropertyOpen] = useState(false);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editProperty, setEditProperty] = useState<CardProperty | null>(null);
  const [newProperty, setNewProperty] = useState<any>({
    name: '',
    address: '',
    city: '',
    state: '',
    zip: '',
    bedrooms: 1,
    bathrooms: 1,
    budget: 0,
    imageUrl: '',
    iCalUrl: '',
    collections: []
  });

  const handleAddProperty = async () => {
    try {
      if (!userId) {
        setTimeout(() => {
          toast.error('You must be logged in to add a property');
        }, 0);
        return;
      }

      // Process collections for database storage
      let collectionsToSave: any[] = [];
      
      if (newProperty.collections && Array.isArray(newProperty.collections)) {
        collectionsToSave = newProperty.collections.map(c => {
          if (typeof c === 'string') return c;
          if (c && typeof c === 'object' && 'name' in c) return c;
          return null;
        }).filter(Boolean);
      }
      
      console.log('Collections being saved:', collectionsToSave);

      // Map from component format to database format
      const dbProperty = {
        user_id: userId,
        name: newProperty.name,
        address: newProperty.address,
        city: newProperty.city,
        state: newProperty.state,
        zip: newProperty.zip,
        bedrooms: newProperty.bedrooms || 1,
        bathrooms: newProperty.bathrooms || 1,
        budget: newProperty.budget || 0,
        image_url: newProperty.imageUrl,
        ical_url: newProperty.iCalUrl,
        next_booking: newProperty.nextBooking,
        collections: collectionsToSave
      };
      
      console.log("Adding new property:", dbProperty);
      
      const { data, error } = await supabase
        .from('properties')
        .insert(dbProperty)
        .select()
        .single();
      
      if (error) {
        console.error("Supabase error:", error);
        throw error;
      }
      
      if (data) {
        console.log("Property added successfully:", data);
        
        // Process collections from the database for UI
        let uiCollections: CollectionWithBudget[] = [];
        
        if (data.collections) {
          if (Array.isArray(data.collections)) {
            uiCollections = data.collections.map((col: any) => {
              if (typeof col === 'string') {
                return { name: col };
              } else if (col && typeof col === 'object' && 'name' in col) {
                return col as CollectionWithBudget;
              }
              return { name: String(col) };
            });
          }
        }
        
        const formattedProperty: Property = {
          id: data.id,
          name: data.name,
          address: data.address,
          city: data.city,
          state: data.state,
          zip: data.zip,
          image_url: data.image_url,
          bedrooms: data.bedrooms || 1,
          bathrooms: data.bathrooms || 1,
          budget: data.budget || 0,
          ical_url: data.ical_url || '',
          next_booking: data.next_booking || '',
          collections: uiCollections,
          last_ical_sync: data.last_ical_sync || ''
        };
        
        setProperties([formattedProperty, ...properties]);
        setTimeout(() => {
          toast.success('Property added successfully');
        }, 0);
        setIsAddPropertyOpen(false);
        
        // Reset the new property form
        setNewProperty({
          name: '',
          address: '',
          city: '',
          state: '',
          zip: '',
          bedrooms: 1,
          bathrooms: 1,
          budget: 0,
          imageUrl: '',
          iCalUrl: '',
          collections: []
        });
        
        // If the property has an iCal URL, fetch calendar data
        if (data.ical_url && userId) {
          fetchCalendarData(data.ical_url, data.id, userId, fetchProperties);
        }
      }
    } catch (error: any) {
      console.error('Error adding property:', error);
      setTimeout(() => {
        toast.error(`Failed to add property: ${error.message || 'Unknown error'}`);
      }, 0);
    }
  };
  
  const handleUpdateProperty = async (propertyId: string, updatedData: Partial<Property>) => {
    try {
      console.log("Updating property with data:", updatedData);
      
      // Process collections for database storage
      let collectionsToSave: any[] = [];
      
      if (updatedData.collections) {
        if (Array.isArray(updatedData.collections)) {
          collectionsToSave = updatedData.collections.map(c => {
            if (typeof c === 'string') return c;
            if (c && typeof c === 'object' && 'name' in c) return c;
            return null;
          }).filter(Boolean);
        }
      }
      
      console.log('Collections to update:', collectionsToSave);
      
      // Ensure the database field names are used
      const dataToUpdate: any = {
        name: updatedData.name,
        address: updatedData.address,
        city: updatedData.city,
        state: updatedData.state,
        zip: updatedData.zip,
        image_url: updatedData.image_url,
        bedrooms: updatedData.bedrooms,
        bathrooms: updatedData.bathrooms,
        budget: updatedData.budget,
        ical_url: updatedData.ical_url,
        next_booking: updatedData.next_booking,
        collections: collectionsToSave
      };
      
      console.log("Data to update in Supabase:", dataToUpdate);
      
      const { error } = await supabase
        .from('properties')
        .update(dataToUpdate)
        .eq('id', propertyId);
      
      if (error) throw error;
      
      console.log("Property updated successfully");
      
      // Refresh the property list after update
      fetchProperties();
      
      // If the iCal URL was updated, fetch the calendar data
      if (userId && updatedData.ical_url && (!selectedProperty?.ical_url || updatedData.ical_url !== selectedProperty.ical_url)) {
        fetchCalendarData(updatedData.ical_url, propertyId, userId, fetchProperties);
      }
      
      setTimeout(() => {
        toast.success('Property updated successfully');
      }, 0);
      setIsDetailOpen(false);
    } catch (error: any) {
      console.error('Error updating property:', error);
      setTimeout(() => {
        toast.error('Failed to update property');
      }, 0);
    }
  };
  
  const handleDeleteProperty = async (propertyId: string) => {
    console.log('handleDeleteProperty called with propertyId:', propertyId);

    // Temporarily disable permission check for debugging
    // if (!hasPermission(PermissionType.MANAGE_PROPERTIES)) {
    //   console.log('Permission denied for MANAGE_PROPERTIES');
    //   setTimeout(() => {
    //     toast.error('You do not have permission to delete properties');
    //   }, 0);
    //   return;
    // }

    console.log('Showing confirmation dialog');
    const confirmed = window.confirm('Are you sure you want to delete this property? This action cannot be undone. All associated inventory items, maintenance tasks, and damage reports will also be deleted.');
    if (!confirmed) {
      console.log('User cancelled deletion');
      return;
    }

    console.log('User confirmed deletion, proceeding...');

    try {
      console.log('Starting deletion process...');
      setTimeout(() => {
        toast.loading('Deleting property and associated data...', { id: 'delete-property' });
      }, 0);

      console.log('Calling delete_property_cascade RPC function...');
      // Use the RPC function to delete the property and all related data
      const { data, error } = await supabase.rpc('delete_property_cascade', {
        property_id_param: propertyId
      });

      console.log('RPC function response:', { data, error });

      if (error) {
        console.error('Error in delete_property_cascade function:', error);
        setTimeout(() => {
          toast.error(`Failed to delete property: ${error.message}`, { id: 'delete-property' });
        }, 0);

        // Fallback to simple deletion
        console.log('Falling back to simple deletion...');

        try {
          // Try simple property deletion
          const { error: simpleError } = await supabase
            .from('properties')
            .delete()
            .eq('id', propertyId);

          if (simpleError) {
            console.error('Error with simple deletion:', simpleError);
            setTimeout(() => {
              toast.error(`Failed to delete property: ${simpleError.message}`, { id: 'delete-property' });
            }, 0);
            return;
          }

          setTimeout(() => {
            toast.success('Property deleted successfully', { id: 'delete-property' });
          }, 0);
          setProperties(properties.filter(property => property.id !== propertyId));
          setIsDetailOpen(false);

          // Refresh the properties list
          if (fetchProperties) {
            fetchProperties();
          }
        } catch (fallbackError: any) {
          console.error('Error in fallback deletion:', fallbackError);
          setTimeout(() => {
            toast.error(`Failed to delete property: ${fallbackError.message || 'Unknown error'}`, { id: 'delete-property' });
          }, 0);
        }
        return;
      }

      setTimeout(() => {
        toast.success('Property and all associated data deleted successfully', { id: 'delete-property' });
      }, 0);

      setProperties(properties.filter(property => property.id !== propertyId));
      setIsDetailOpen(false);

      // Refresh the properties list
      if (fetchProperties) {
        fetchProperties();
      }
    } catch (error: any) {
      console.error('Error deleting property:', error);
      setTimeout(() => {
        toast.error(`Failed to delete property: ${error.message || 'Unknown error'}`, { id: 'delete-property' });
      }, 0);
    }
  };
  
  const handleViewProperty = (property: Property) => {
    setSelectedProperty(property);
    setIsDetailOpen(true);
  };
  
  return {
    isAddPropertyOpen,
    setIsAddPropertyOpen,
    isDetailOpen,
    setIsDetailOpen,
    selectedProperty,
    setSelectedProperty,
    isEditMode,
    setIsEditMode,
    editProperty,
    setEditProperty,
    newProperty,
    setNewProperty,
    handleAddProperty,
    handleUpdateProperty,
    handleDeleteProperty,
    handleViewProperty
  };
};
